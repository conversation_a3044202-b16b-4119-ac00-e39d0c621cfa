
services:
  essearch-mcp:
    # build: .
    image: mcpessearch:latest
    container_name: essearch-mcp-server
    ports:
      - "8005:8005"
    environment:
      - ES_HOST=${ES_HOST:-**************}
      - ES_PORT=${ES_PORT:-9600}
      - ES_USERNAME=${ES_USERNAME:-}
      - ES_PASSWORD=${ES_PASSWORD:-}
      - ES_USE_SSL=${ES_USE_SSL:-false}
      - ES_VERIFY_CERTS=${ES_VERIFY_CERTS:-false}
    env_file:
      - .env
    restart: unless-stopped
    networks:
      - essearch-network


networks:
  essearch-network:
    driver: bridge

