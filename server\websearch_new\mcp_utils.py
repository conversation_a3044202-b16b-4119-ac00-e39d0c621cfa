#!/usr/bin/env python3
"""
MCP工具调用工具类
使用MultiServerMCPClient来调用MCP工具
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_mcp_adapters.tools import load_mcp_tools

logger = logging.getLogger(__name__)

class MCPToolCaller:
    """MCP工具调用器"""

    def __init__(self):
        self.client = None
        self.server_configs = {}
        self.tools = []

    def add_server(self, server_name: str, ip: str, port: int, transport: str = "sse"):
        """
        添加MCP服务器配置

        Args:
            server_name: 服务器名称
            ip: 服务器IP地址
            port: 服务器端口
            transport: 传输方式，支持 "sse" 或 "stdio"
        """
        if transport == "sse":
            self.server_configs[server_name] = {
                "url": f"http://{ip}:{port}/sse",
                "transport": "sse"
            }
        elif transport == "stdio":
            # stdio方式需要命令和参数
            self.server_configs[server_name] = {
                "command": "python",
                "args": [f"server_{server_name}.py"],  # 假设服务器脚本名称
                "transport": "stdio"
            }
        else:
            raise ValueError(f"不支持的传输方式: {transport}")

    async def initialize(self):
        """初始化客户端和工具"""
        if not self.server_configs:
            raise ValueError("请先添加至少一个服务器配置")

        self.client = MultiServerMCPClient(self.server_configs)

        # 获取所有工具
        try:
            self.tools = await self.client.get_tools()
            logger.info(f"成功加载 {len(self.tools)} 个工具")
        except Exception as e:
            logger.error(f"加载工具失败: {e}")
            self.tools = []

    async def cleanup(self):
        """清理资源"""
        if self.client:
            # 新版本的MultiServerMCPClient可能需要不同的清理方式
            try:
                if hasattr(self.client, 'close'):
                    await self.client.close()
            except Exception as e:
                logger.warning(f"清理客户端时出错: {e}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup()
    
    async def call_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """
        调用MCP工具

        Args:
            tool_name: 工具名称
            **kwargs: 工具参数

        Returns:
            Dict[str, Any]: 工具调用结果
        """
        if not self.tools:
            raise RuntimeError("工具未初始化，请使用 async with 语句")

        try:
            # 查找指定工具
            target_tool = None
            for tool in self.tools:
                if tool.name == tool_name:
                    target_tool = tool
                    break

            if not target_tool:
                available_tools = [tool.name for tool in self.tools]
                return {
                    "error": "tool_not_found",
                    "message": f"工具 '{tool_name}' 未找到",
                    "available_tools": available_tools
                }

            # 调用工具
            result = await target_tool.ainvoke(kwargs)
            return {
                "success": True,
                "result": result,
                "tool_name": tool_name,
                "parameters": kwargs
            }

        except Exception as e:
            logger.error(f"调用工具 {tool_name} 失败: {str(e)}")
            return {
                "error": "tool_call_failed",
                "message": f"调用工具失败: {str(e)}",
                "tool_name": tool_name,
                "parameters": kwargs
            }

    async def list_tools(self) -> List[Dict[str, Any]]:
        """
        列出所有可用工具

        Returns:
            List[Dict[str, Any]]: 工具列表
        """
        if not self.tools:
            raise RuntimeError("工具未初始化，请使用 async with 语句")

        try:
            tool_list = []

            for tool in self.tools:
                tool_info = {
                    "name": tool.name,
                    "description": getattr(tool, 'description', ''),
                    "parameters": getattr(tool, 'args_schema', {})
                }
                tool_list.append(tool_info)

            return tool_list

        except Exception as e:
            logger.error(f"获取工具列表失败: {str(e)}")
            return []


# 便捷函数
async def call_mcp_tool(ip: str, port: int, tool_name: str, server_name: str = "default",
                       transport: str = "sse", **kwargs) -> Dict[str, Any]:
    """
    便捷函数：调用单个MCP工具

    Args:
        ip: 服务器IP地址
        port: 服务器端口
        tool_name: 工具名称
        server_name: 服务器名称（可选）
        transport: 传输方式，默认为"sse"
        **kwargs: 工具参数

    Returns:
        Dict[str, Any]: 工具调用结果
    """
    caller = MCPToolCaller()
    caller.add_server(server_name, ip, port, transport)

    async with caller as client:
        return await client.call_tool(tool_name, **kwargs)


async def list_mcp_tools(ip: str, port: int, server_name: str = "default",
                        transport: str = "sse") -> List[Dict[str, Any]]:
    """
    便捷函数：列出MCP服务器的所有工具

    Args:
        ip: 服务器IP地址
        port: 服务器端口
        server_name: 服务器名称（可选）
        transport: 传输方式，默认为"sse"

    Returns:
        List[Dict[str, Any]]: 工具列表
    """
    caller = MCPToolCaller()
    caller.add_server(server_name, ip, port, transport)

    async with caller as client:
        return await client.list_tools()


# 示例使用
async def example_usage():
    """使用示例"""
    
    # 方式1: 使用便捷函数调用单个工具
    print("=== 使用便捷函数调用工具 ===")
    result = await call_mcp_tool(
        ip="localhost",
        port=8001,
        tool_name="get_weather",
        location="北京"
    )
    print(f"天气查询结果: {result}")
    
    # 方式2: 使用MCPToolCaller类管理多个服务器
    print("\n=== 使用MCPToolCaller管理多个服务器 ===")
    caller = MCPToolCaller()
    
    # 添加多个服务器
    caller.add_server("weather", "localhost", 8001)
    caller.add_server("math", "localhost", 8000)
    caller.add_server("entity", "localhost", 8002)
    
    async with caller as client:
        # 列出所有工具
        tools = await client.list_tools()
        print(f"可用工具: {[tool['name'] for tool in tools]}")
        
        # 调用不同服务器的工具
        weather_result = await client.call_tool("get_weather", location="上海")
        print(f"天气结果: {weather_result}")
        
        math_result = await client.call_tool("add", a=10, b=20)
        print(f"数学计算结果: {math_result}")
        
        entity_result = await client.call_tool("get_entity_info", company_alias="腾讯")
        print(f"实体信息结果: {entity_result}")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(example_usage())
