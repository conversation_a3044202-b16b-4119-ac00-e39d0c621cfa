#!/usr/bin/env python3
"""
简单测试 web_search 接口
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "roardata_mcp-develop-fe0cba7eb1ea6d2c9d1581a7f91e5af9ba5f5198", "server", "websearch"))

from websearch_mcp_server import web_search

async def test():
    print("测试 web_search 接口...")
    result = await web_search(query ="Python编程",freshness = "noLimit", count = "3", include = "None", exclude = "None", summary = "false")
    print("结果:", result)

if __name__ == "__main__":
    asyncio.run(test())
