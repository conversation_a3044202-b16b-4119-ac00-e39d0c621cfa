#!/usr/bin/env python3
"""
简单的MCP工具调用示例
演示如何使用mcp_utils调用MCP工具
"""

import asyncio
from mcp_utils import call_mcp_tool, list_mcp_tools, MCPToolCaller

async def example_1_simple_call():
    """示例1: 简单的工具调用"""
    print("=== 示例1: 简单工具调用 ===")
    
    # 调用web搜索工具
    result = await call_mcp_tool(
        ip="localhost",
        port=8003,  # websearch服务端口
        tool_name="web_search",
        query="Python编程教程",
        count=3
    )
    
    if result.get("success"):
        print("✅ 调用成功!")
        print(f"结果: {result['result']}")
    else:
        print("❌ 调用失败!")
        print(f"错误: {result.get('message', '未知错误')}")

async def example_2_list_tools():
    """示例2: 列出所有可用工具"""
    print("\n=== 示例2: 列出可用工具 ===")
    
    tools = await list_mcp_tools(
        ip="localhost",
        port=8003
    )
    
    print(f"找到 {len(tools)} 个工具:")
    for tool in tools:
        print(f"  📋 {tool['name']}: {tool.get('description', '无描述')}")

async def example_3_multiple_calls():
    """示例3: 使用MCPToolCaller进行多次调用"""
    print("\n=== 示例3: 多次工具调用 ===")
    
    caller = MCPToolCaller()
    caller.add_server("websearch", "localhost", 8003)
    
    async with caller as client:
        # 第一次调用
        result1 = await client.call_tool(
            "web_search",
            query="人工智能",
            count=2
        )
        print(f"🔍 第一次搜索: {'成功' if result1.get('success') else '失败'}")
        
        # 第二次调用
        result2 = await client.call_tool(
            "web_search", 
            query="机器学习",
            count=2
        )
        print(f"🔍 第二次搜索: {'成功' if result2.get('success') else '失败'}")

async def example_4_multiple_servers():
    """示例4: 连接多个服务器"""
    print("\n=== 示例4: 多服务器连接 ===")
    
    caller = MCPToolCaller()
    
    # 添加多个服务器
    caller.add_server("websearch", "localhost", 8003)
    caller.add_server("weather", "localhost", 8001)
    caller.add_server("entity", "localhost", 8002)
    
    async with caller as client:
        # 列出所有工具
        tools = await client.list_tools()
        print(f"📚 总共找到 {len(tools)} 个工具")
        
        # 尝试调用不同服务器的工具
        services = [
            ("web_search", {"query": "新闻", "count": 1}),
            ("get_weather", {"location": "北京"}),
            ("get_entity_info", {"company_alias": "腾讯"})
        ]
        
        for tool_name, params in services:
            result = await client.call_tool(tool_name, **params)
            status = "✅ 成功" if result.get("success") else "❌ 失败"
            print(f"  🔧 {tool_name}: {status}")

async def main():
    """主函数"""
    print("🚀 MCP工具调用示例")
    print("=" * 40)
    
    try:
        await example_1_simple_call()
        await example_2_list_tools()
        await example_3_multiple_calls()
        await example_4_multiple_servers()
        
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        print("\n💡 请确保:")
        print("  1. MCP服务器正在运行")
        print("  2. IP和端口配置正确")
        print("  3. 已安装所需依赖")
    
    print("\n" + "=" * 40)
    print("✨ 示例运行完成!")

if __name__ == "__main__":
    asyncio.run(main())
