from typing import List, Optional, Dict, Any
import os
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from fastapi import FastAPI
import uvicorn
import traceback
import logging
import pymysql
from pymysql.cursors import DictCursor

logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# MySQL配置
MYSQL_HOST = "***********"
MYSQL_PORT = 5718
MYSQL_USER = "jiachengbin"
MYSQL_PASSWORD = "aiZ3Ooc2aing<ai"
MYSQL_DATABASE = "pom_terminal"

mcp = FastMCP("MySQLSearchService")

def get_mysql_connection():
    """获取MySQL连接"""
    try:
        connection = pymysql.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DATABASE,
            charset='utf8mb4',
            cursorclass=DictCursor,
            connect_timeout=10,
            read_timeout=30,
            write_timeout=30
        )
        logger.info("MySQL连接成功")
        return connection
    except Exception as e:
        logger.error(f"MySQL连接失败: {str(e)}")
        return None

@mcp.tool()
async def get_entity_id(company_name: str, limit: Optional[int] = 5) -> dict:
    """
    根据公司名称查询公司信息
    Args:
        company_name: 公司名称，支持精确匹配和模糊匹配
        limit: 返回结果数量限制，默认5条
    Returns:
        dict: 查询结果，包含company_id和company_name字段列表
    """
    if not company_name or not company_name.strip():
        return {
            "error": "invalid_parameter",
            "message": "公司名称不能为空"
        }
    
    company_name = company_name.strip()
    connection = get_mysql_connection()
    
    if not connection:
        return {
            "error": "database_connection_failed",
            "message": "无法连接到MySQL数据库"
        }
    
    try:
        with connection.cursor() as cursor:
            # 首先尝试精确匹配 company_name 或 alias
            exact_sql = """
                SELECT company_id, company_name 
                FROM cbirc_company 
                WHERE company_name = %s OR alias = %s
                LIMIT %s
            """
            cursor.execute(exact_sql, (company_name, company_name, limit))
            exact_results = cursor.fetchall()
            
            if exact_results:
                logger.info(f"精确匹配找到 {len(exact_results)} 条记录")
                return {
                    "success": True,
                    "match_type": "exact",
                    "query": company_name,
                    "count": len(exact_results),
                    "data": exact_results
                }
            
            # 如果精确匹配没有结果，使用模糊匹配
            fuzzy_sql = """
                SELECT company_id, company_name 
                FROM cbirc_company 
                WHERE company_name LIKE %s OR alias LIKE %s
                LIMIT %s
            """
            fuzzy_pattern = f"%{company_name}%"
            cursor.execute(fuzzy_sql, (fuzzy_pattern, fuzzy_pattern, limit))
            fuzzy_results = cursor.fetchall()
            
            if fuzzy_results:
                logger.info(f"模糊匹配找到 {len(fuzzy_results)} 条记录")
                return {
                    "success": True,
                    "match_type": "fuzzy",
                    "query": company_name,
                    "count": len(fuzzy_results),
                    "data": fuzzy_results
                }
            else:
                logger.info("未找到匹配的公司记录")
                return {
                    "success": True,
                    "match_type": "none",
                    "query": company_name,
                    "count": 0,
                    "data": [],
                    "message": "未找到匹配的公司记录"
                }
                
    except pymysql.Error as e:
        logger.error(f"MySQL查询错误: {str(e)}")
        return {
            "error": "mysql_query_error",
            "message": f"MySQL查询错误: {str(e)}"
        }
    except Exception as e:
        logger.error(f"查询过程中出现未知错误: {str(e)}")
        traceback.print_exc()
        return {
            "error": "unknown_error",
            "message": f"查询过程中出现未知错误: {str(e)}"
        }
    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    app = FastAPI()
    app.mount("/", mcp.sse_app())
    uvicorn.run(app, host="0.0.0.0", port=8006)
