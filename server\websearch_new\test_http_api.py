#!/usr/bin/env python3
"""
WebSearch HTTP API 测试脚本
测试新增的 HTTP 和流式 API 功能
"""

import asyncio
import aiohttp
import json
import sys
import time

BASE_URL = "http://localhost:8003"

async def test_health_check():
    """测试健康检查端点"""
    print("=== 测试健康检查 ===")
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 健康检查成功: {data}")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False

async def test_api_docs():
    """测试 API 文档端点"""
    print("\n=== 测试 API 文档 ===")
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/api/docs") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ API 文档获取成功:")
                    print(f"  服务: {data.get('service')}")
                    print(f"  版本: {data.get('version')}")
                    print(f"  可用端点: {len(data.get('endpoints', {}).get('http_apis', {}))}")
                    return True
                else:
                    print(f"❌ API 文档获取失败: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False

async def test_http_web_search():
    """测试 HTTP 网络搜索"""
    print("\n=== 测试 HTTP 网络搜索 ===")
    async with aiohttp.ClientSession() as session:
        try:
            payload = {
                "query": "Python编程教程",
                "freshness": "oneWeek",
                "summary": "true",
                "count": "3"
            }
            
            print(f"发送请求: {payload}")
            start_time = time.time()
            
            async with session.post(f"{BASE_URL}/api/web-search", json=payload) as response:
                end_time = time.time()
                
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 搜索成功 (耗时: {end_time - start_time:.2f}s)")
                    print(f"  成功状态: {data.get('success')}")
                    if data.get('success'):
                        search_data = data.get('data', {})
                        print(f"  结果数量: {len(search_data.get('results', []))}")
                    else:
                        print(f"  错误: {data.get('error')} - {data.get('message')}")
                    return True
                else:
                    print(f"❌ 搜索失败: {response.status}")
                    text = await response.text()
                    print(f"  响应: {text}")
                    return False
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False

async def test_stream_web_search():
    """测试流式网络搜索"""
    print("\n=== 测试流式网络搜索 ===")
    async with aiohttp.ClientSession() as session:
        try:
            payload = {
                "query": "人工智能最新发展",
                "count": "2"
            }
            
            print(f"发送流式请求: {payload}")
            
            async with session.post(f"{BASE_URL}/api/web-search/stream", json=payload) as response:
                if response.status == 200:
                    print("✅ 开始接收流式数据:")
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            try:
                                data = json.loads(line[6:])  # 去掉 'data: ' 前缀
                                msg_type = data.get('type')
                                message = data.get('message', '')
                                
                                if msg_type == 'start':
                                    print(f"  🚀 {message}")
                                elif msg_type == 'progress':
                                    print(f"  ⏳ {message}")
                                elif msg_type == 'result':
                                    print(f"  📊 获得搜索结果")
                                    result_data = data.get('data', {})
                                    results = result_data.get('results', [])
                                    print(f"     结果数量: {len(results)}")
                                elif msg_type == 'done':
                                    print(f"  ✅ {message}")
                                    break
                                elif msg_type == 'error':
                                    print(f"  ❌ 错误: {data.get('error')} - {message}")
                                    break
                                    
                            except json.JSONDecodeError:
                                continue
                    
                    return True
                else:
                    print(f"❌ 流式搜索失败: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 流式请求失败: {e}")
            return False

async def test_baidu_search():
    """测试百度搜索"""
    print("\n=== 测试百度搜索 ===")
    async with aiohttp.ClientSession() as session:
        try:
            payload = {
                "query": "天气预报"
            }
            
            print(f"发送百度搜索请求: {payload}")
            
            async with session.post(f"{BASE_URL}/api/baidu-search", json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 百度搜索响应")
                    print(f"  成功状态: {data.get('success')}")
                    if not data.get('success'):
                        print(f"  错误: {data.get('error')} - {data.get('message')}")
                    return True
                else:
                    print(f"❌ 百度搜索失败: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 百度搜索请求失败: {e}")
            return False

async def main():
    """主测试函数"""
    print("WebSearch HTTP API 测试开始...")
    print("=" * 50)
    
    # 测试序列
    tests = [
        ("健康检查", test_health_check),
        ("API 文档", test_api_docs),
        ("HTTP 网络搜索", test_http_web_search),
        ("流式网络搜索", test_stream_web_search),
        ("百度搜索", test_baidu_search),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
        
        # 测试间隔
        await asyncio.sleep(1)
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！HTTP API 功能正常")
    else:
        print("⚠️  部分测试失败，请检查服务器状态和配置")

if __name__ == "__main__":
    print("请确保 WebSearch MCP Server 正在运行在 http://localhost:8003")
    print("启动命令: python websearch_mcp_server.py")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试执行异常: {e}")
