# encoding=utf-8

from typing import List, Optional, Dict, Any
import os
import json
from datetime import datetime
from dateutil.relativedelta import relativedelta
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from fastapi import FastAPI
import uvicorn
from elasticsearch import Elasticsearch
import traceback
import logging

logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# Elasticsearch配置
# ES_HOST = os.getenv("ES_HOST", "************")
# ES_PORT = int(os.getenv("ES_PORT", "9200"))
# ES_USERNAME = os.getenv("ES_USERNAME", "admin")
# ES_PASSWORD = os.getenv("ES_PASSWORD", "WiseWeb@666")
# ES_INDEX = os.getenv("ES_INDEX", "ybj_zy_data_articles_alias")

# ES_HOST = "************"
# ES_PORT = 9200
# ES_USERNAME = "admin"
# ES_PASSWORD = "WiseWeb@666"
# ES_INDEX = "ybj_zy_data_articles_alias"

ES_HOST = "************"
ES_PORT = 9200
ES_USERNAME = "jiachengbin"
ES_PASSWORD = "jcb1qaz@WSX#EDC$RFV"
ES_INDEX = "ybj_zy_data_articles_alias"

mcp = FastMCP("YuqingSearchService")

# 初始化Elasticsearch客户端
def get_es_client():
    """获取Elasticsearch客户端"""
    try:
        if ES_USERNAME and ES_PASSWORD:
            es = Elasticsearch(
                [{"host": ES_HOST, "port": ES_PORT}],
                http_auth=(ES_USERNAME, ES_PASSWORD),
                timeout=30,
                max_retries=3,
                retry_on_timeout=True
            )
        else:
            es = Elasticsearch(
                [{"host": ES_HOST, "port": ES_PORT}],
                timeout=30,
                max_retries=3,
                retry_on_timeout=True
            )
        
        # 测试连接
        if es.ping():
            logger.info("Elasticsearch连接成功")
            return es
        else:
            logger.error("Elasticsearch连接失败")
            return None
    except Exception as e:
        logger.error(f"创建Elasticsearch客户端失败: {str(e)}")
        return None

# 获取过去的时间，无参数获取当前时间
def get_ago_date(years=None, months=None, weeks=None, days=None):
    """获取过去的时间"""
    current_date = datetime.now()
    if years:
        ago_time = relativedelta(years=years)
    elif months:
        ago_time = relativedelta(months=months)
    elif weeks:
        ago_time = relativedelta(weeks=weeks)
    elif days:
        ago_time = relativedelta(days=days)
    else:
        return current_date.strftime("%Y-%m-%d %H:%M:%S")
    
    ago_date = (current_date - ago_time).strftime("%Y-%m-%d %H:%M:%S")
    return ago_date

# 构建查询体
def build_es_query(nested_dict, agg_dict=None, min_date=None, max_date=None, sort_desc=True, size=10):
    """构建Elasticsearch查询体"""
    date_field = nested_dict.get("date_field", "Pub_Time")
    
    query_body = {
        "track_total_hits": True,
        "query": {
            "bool": {
                "must": [],
                "filter": []
            }
        },
        "size": size
    }
    
    # nested嵌套查询
    if "entity_id" in nested_dict or "entity_id_list" in nested_dict:
        nested_query = {
            "nested": {
                "path": nested_dict["nested_path"],
                "query": {
                    "bool": {
                        "should": [],
                    }
                }
            }
        }
        
        match_field = f"{nested_dict['nested_path']}.{nested_dict['nested_path_type']}"
        
        if "entity_id" in nested_dict:
            nested_query["nested"]["query"]["bool"]["should"].append({
                "match": {
                    match_field: nested_dict["entity_id"]
                }
            })
        elif "entity_id_list" in nested_dict:
            for nested_id in nested_dict["entity_id_list"]:
                nested_query["nested"]["query"]["bool"]["should"].append({
                    "match": {
                        match_field: nested_id
                    }
                })
        
        query_body["query"]["bool"]["must"].append(nested_query)
    
    # 时间范围过滤
    if min_date:
        query_body["query"]["bool"]["filter"].append({
            "range": {
                date_field: {
                    "gte": min_date
                }
            }
        })
    
    if max_date:
        query_body["query"]["bool"]["filter"].append({
            "range": {
                date_field: {
                    "lte": max_date
                }
            }
        })
    
    # 排序
    if not agg_dict and sort_desc:
        query_body["sort"] = [
            {
                date_field: {
                    "order": "desc"
                }
            }
        ]
    
    # 聚合查询
    if agg_dict:
        if agg_dict["agg_type"] == "date_histogram":
            query_body["aggs"] = {
                agg_dict["agg_name"]: {
                    agg_dict["agg_type"]: {
                        "field": agg_dict["agg_field"],
                        "calendar_interval": agg_dict["agg_interval"],
                        "format": agg_dict["format"]
                    }
                }
            }
    
    return query_body

# 解析response
def parse_response(response):
    """解析Elasticsearch响应"""
    if not response["hits"]["hits"]:
        return []
    
    media_type_dict = {
    1: "新闻",
    2: "论坛",
    3: "博客",
    4: "微博",
    5: "纸媒",
    6: "视频",
    11: "微信",
    13: "客户端"
    }
    
    sentiment_data = []
    for hit in response["hits"]["hits"]:
        doc_source = hit["_source"]
        document = {
            "id": doc_source.get("Rowkey", ""),
            "entity_id": doc_source.get("New_Module_Ids", ""),
            "content": doc_source.get("Content", ""),
            "source": media_type_dict[doc_source.get("Media_Type", "")],
            "sentiment_label": doc_source.get("Ner_Flag", ""),
            "publish_time": doc_source.get("Pub_Time", ""),
            "url": doc_source.get("Url", ""),
            "title": doc_source.get("Title", "")
        }
        sentiment_data.append(document)
    
    return sentiment_data

@mcp.tool()
async def get_sentiment_data(
    entity_id: str,
    limit: Optional[int] = 5
) -> dict:
    """
    获取单个实体舆情数据
    Args:
        entity_id: 实体ID
        limit: 返回条数限制，默认10
    Returns:
        dict: 舆情数据结果
    """
    es = get_es_client()
    if not es:
        return {"error": "elasticsearch_connection_failed", "message": "无法连接到Elasticsearch"}
    
    try:
        nested_dict = {
            "entity_id": entity_id,
            "date_field": "Pub_Time",
            "nested_path": "New_Module_Ids",
            "nested_path_type": "company"
        }
        
        query = build_es_query(nested_dict=nested_dict, size=limit)
        response = es.search(index=ES_INDEX, body=query, request_timeout=60)
        sentiment_data = parse_response(response)
        
        return {
            "sentiment_data": sentiment_data,
            "total": response["hits"]["total"]["value"]
        }
        
    except Exception as e:
        logger.error(f"获取舆情数据失败: {str(e)}")
        traceback.print_exc()
        return {
            "error": "search_failed",
            "message": f"获取舆情数据失败: {str(e)}"
        }

@mcp.tool()
async def get_sentiment_list(
    entity_ids: List[str],
    limit_per_entity: Optional[int] = 5
) -> dict:
    """
    批量获取多实体舆情数据
    Args:
        entity_ids: 实体ID列表
        limit_per_entity: 每个实体返回条数，默认5
    Returns:
        dict: 舆情数据结果
    """
    es = get_es_client()
    if not es:
        return {"error": "elasticsearch_connection_failed", "message": "无法连接到Elasticsearch"}

    try:
        nested_dict = {
            "entity_id_list": entity_ids,
            "date_field": "Pub_Time",
            "nested_path": "New_Module_Ids",
            "nested_path_type": "company"
        }

        total_size = len(entity_ids) * limit_per_entity
        query = build_es_query(nested_dict=nested_dict, size=total_size)
        response = es.search(index=ES_INDEX, body=query, request_timeout=60)
        sentiment_data = parse_response(response)

        return {
            "sentiment_data": sentiment_data,
            "total": response["hits"]["total"]["value"]
        }

    except Exception as e:
        logger.error(f"批量获取舆情数据失败: {str(e)}")
        traceback.print_exc()
        return {
            "error": "search_failed",
            "message": f"批量获取舆情数据失败: {str(e)}"
        }

@mcp.tool()
async def get_sentiment_by_time_range(
    entity_id: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    limit: Optional[int] = 10
) -> dict:
    """
    根据时间范围获取实体舆情数据
    Args:
        entity_id: 实体ID
        start_time: 开始时间，格式：YYYY-MM-DD HH:MM:SS
        end_time: 结束时间，格式：YYYY-MM-DD HH:MM:SS
        limit: 返回条数限制，默认10
    Returns:
        dict: 舆情数据结果
    """
    es = get_es_client()
    if not es:
        return {"error": "elasticsearch_connection_failed", "message": "无法连接到Elasticsearch"}

    try:
        nested_dict = {
            "entity_id": entity_id,
            "date_field": "Pub_Time",
            "nested_path": "New_Module_Ids",
            "nested_path_type": "company"
        }

        query = build_es_query(
            nested_dict=nested_dict,
            min_date=start_time,
            max_date=end_time,
            size=limit
        )
        response = es.search(index=ES_INDEX, body=query, request_timeout=60)
        sentiment_data = parse_response(response)

        return {
            "sentiment_data": sentiment_data,
            "total": response["hits"]["total"]["value"],
            "time_range": {
                "start_time": start_time,
                "end_time": end_time
            }
        }

    except Exception as e:
        logger.error(f"根据时间范围获取舆情数据失败: {str(e)}")
        traceback.print_exc()
        return {
            "error": "search_failed",
            "message": f"根据时间范围获取舆情数据失败: {str(e)}"
        }

@mcp.tool()
async def get_sentiment_aggregation(
    entity_id: str,
    agg_interval: Optional[str] = "day",
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
) -> dict:
    """
    获取实体舆情数据聚合统计
    Args:
        entity_id: 实体ID
        agg_interval: 聚合间隔，支持：day、week、month、year
        start_time: 开始时间，格式：YYYY-MM-DD HH:MM:SS
        end_time: 结束时间，格式：YYYY-MM-DD HH:MM:SS
    Returns:
        dict: 聚合统计结果
    """
    es = get_es_client()
    if not es:
        return {"error": "elasticsearch_connection_failed", "message": "无法连接到Elasticsearch"}

    try:
        nested_dict = {
            "entity_id": entity_id,
            "date_field": "Pub_Time",
            "nested_path": "New_Module_Ids",
            "nested_path_type": "company"
        }

        agg_dict = {
            "agg_name": "dates_aggregation",
            "agg_type": "date_histogram",
            "agg_field": "Pub_Time",
            "agg_interval": agg_interval,
            "format": "yyyy-MM-dd"
        }

        query = build_es_query(
            nested_dict=nested_dict,
            agg_dict=agg_dict,
            min_date=start_time,
            max_date=end_time,
            size=0
        )
        response = es.search(index=ES_INDEX, body=query, request_timeout=60)

        # 解析聚合结果
        agg_data = []
        if "aggregations" in response and "dates_aggregation" in response["aggregations"]:
            buckets = response["aggregations"]["dates_aggregation"]["buckets"]
            for bucket in buckets:
                agg_data.append({
                    "date": bucket["key_as_string"],
                    "doc_count": bucket["doc_count"]
                })

        return {
            "aggregation_data": agg_data,
            "total": response["hits"]["total"]["value"],
            "agg_interval": agg_interval,
            "time_range": {
                "start_time": start_time,
                "end_time": end_time
            }
        }

    except Exception as e:
        logger.error(f"获取舆情聚合统计失败: {str(e)}")
        traceback.print_exc()
        return {
            "error": "search_failed",
            "message": f"获取舆情聚合统计失败: {str(e)}"
        }

@mcp.tool()
async def health_check() -> dict:
    """
    健康检查
    Returns:
        dict: 服务状态
    """
    es = get_es_client()
    if not es:
        return {
            "status": "unhealthy",
            "elasticsearch": "disconnected",
            "message": "无法连接到Elasticsearch"
        }

    try:
        # 检查索引是否存在
        index_exists = es.indices.exists(index=ES_INDEX)

        return {
            "status": "healthy",
            "elasticsearch": "connected",
            "index_exists": index_exists,
            "index_name": ES_INDEX,
            "es_host": ES_HOST,
            "es_port": ES_PORT
        }

    except Exception as e:
        return {
            "status": "unhealthy",
            "elasticsearch": "error",
            "message": f"健康检查失败: {str(e)}"
        }

if __name__ == "__main__":
    app = FastAPI()
    app.mount("/", mcp.sse_app())
    uvicorn.run(app, host="0.0.0.0", port=8007)
