#!/usr/bin/env python3
"""
WebSearch MCP Server 启动脚本
支持多种调用方式的网络搜索服务
"""

import os
import sys
import asyncio
from pathlib import Path

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查 .env 文件
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env 文件不存在，创建示例配置...")
        with open(".env", "w", encoding="utf-8") as f:
            f.write("""# WebSearch MCP Server 配置
BOCHAAI_API_KEY=your_bochaai_api_key_here
BAIDU_API_SIGN=your_baidu_api_sign_here
SERVER_PORT=8003
""")
        print("✅ 已创建 .env 示例文件，请配置您的 API 密钥")
        return False
    
    # 检查必要的环境变量
    from dotenv import load_dotenv
    load_dotenv()
    
    bochaai_key = os.getenv("BOCHAAI_API_KEY")
    if not bochaai_key or bochaai_key == "your_bochaai_api_key_here":
        print("⚠️  请在 .env 文件中配置 BOCHAAI_API_KEY")
        return False
    
    print("✅ 环境配置检查通过")
    return True

def show_startup_info():
    """显示启动信息"""
    port = os.getenv("SERVER_PORT", "8003")
    
    print("\n" + "=" * 60)
    print("🚀 WebSearch MCP Server 启动中...")
    print("=" * 60)
    print(f"📡 服务端口: {port}")
    print(f"🌐 服务地址: http://localhost:{port}")
    print("\n📋 可用接口:")
    print(f"  • MCP SSE:      http://localhost:{port}/sse")
    print(f"  • HTTP API:     http://localhost:{port}/api/web-search")
    print(f"  • 流式 API:     http://localhost:{port}/api/web-search/stream")
    print(f"  • API 文档:     http://localhost:{port}/api/docs")
    print(f"  • 健康检查:     http://localhost:{port}/health")
    print("\n🔧 支持的调用方式:")
    print("  1. MCP Client (SSE) - 与 LangChain MCP 适配器集成")
    print("  2. HTTP REST API - 标准 HTTP POST 请求")
    print("  3. 流式 HTTP API - Server-Sent Events 流式响应")
    print("\n📖 使用示例:")
    print("  • 测试 HTTP API: python test_http_api.py")
    print("  • 查看文档: curl http://localhost:8003/api/docs")
    print("=" * 60)

def main():
    """主函数"""
    print("WebSearch MCP Server 启动脚本")
    print("支持 MCP SSE、HTTP REST 和流式 API")
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请配置后重试")
        sys.exit(1)
    
    # 显示启动信息
    show_startup_info()
    
    # 导入并启动服务器
    try:
        print("\n🔄 正在启动服务器...")
        
        # 这里直接运行服务器文件
        import subprocess
        result = subprocess.run([
            sys.executable, "websearch_mcp_server.py"
        ], cwd=Path(__file__).parent)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
