from typing import List, Optional, Dict, Any
import os
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMC<PERSON>
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn
import requests
import traceback
import logging
import json
import asyncio
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

# HTTP API 数据模型
class WebSearchRequest(BaseModel):
    """Web搜索请求模型"""
    query: str
    freshness: Optional[str] = "noLimit"
    summary: Optional[str] = "false"
    include: Optional[str] = None
    exclude: Optional[str] = None
    count: Optional[str] = "10"

class BaiduSearchRequest(BaseModel):
    """百度搜索请求模型"""
    query: str

class WebSearchResponse(BaseModel):
    """搜索响应模型"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    message: Optional[str] = None

# 全局状态管理
class MCPServerState:
    def __init__(self):
        self.initialized = False
        self.initialization_lock = asyncio.Lock()

    async def ensure_initialized(self):
        if not self.initialized:
            async with self.initialization_lock:
                if not self.initialized:
                    # 确保 MCP 服务器完全初始化
                    await asyncio.sleep(0.5)
                    self.initialized = True
                    logger.info("MCP Server state initialized")

server_state = MCPServerState()

# 加载环境变量
load_dotenv()

# 测试使用
BAIDU_API_URL = "https://qianfan.baidubce.com/v2/ai_search/chat/completions"
BAIDU_API_SIGN = os.getenv("BAIDU_API_SIGN", "")

# API配置
BOCHAAI_API_URL = "https://api.bochaai.com/v1/web-search"
BOCHAAI_API_KEY = os.getenv("BOCHAAI_API_KEY", "sk-58b45a4f3d69486e8e08cf98ec85b3d6")

# 创建 FastMCP 实例，添加更详细的配置
mcp = FastMCP(
    name="WebSearchService",
    version="1.0.0",
    description="Web search service using BOCHAAI and Baidu APIs",
    # 添加更多配置以提高稳定性
    timeout=60.0,
    max_connections=100
)

@mcp.tool()
async def web_search(
    query: str,
    freshness: Optional[str] = "noLimit",
    summary: Optional[str] = "false",
    include: Optional[str] = None,
    exclude: Optional[str] = None,
    count: Optional[str] = "10",
) -> dict:
    """
    使用博查AI API进行网络搜索
    Args:
        query: 搜索查询词
        freshness: 搜索指定时间范围内的网页。noLimit，不限（默认）oneDay，oneWeek，oneMonth，oneYear，- YYYY-MM-DD..YYYY-MM-DD，搜索日期范围，例如："2025-01-01..2025-04-06"- YYYY-MM-DD，搜索指定日期，例如："2025-04-06"
        summary: 是否显示文本摘要（字符串："true"/"false"）
        include: 包含的域名列表。指定搜索的网站范围。多个域名使用|或,分隔，最多不能超过20个
        exclude: 排除的域名列表。排除搜索的网站范围。多个域名使用|或,分隔，最多不能超过20个
        count: 返回结果数量（字符串格式），默认"10"
    Returns:
        dict: 搜索结果
    """
    # 记录接收到的参数
    logger.info(f"web_search called with: query='{query}', freshness='{freshness}', summary='{summary}', include='{include}', exclude='{exclude}', count='{count}'")

    # 确保 MCP 服务器已初始化
    await server_state.ensure_initialized()

    if not BOCHAAI_API_KEY:
        return {"error": "api_key_missing", "message": "BOCHAAI_API_KEY环境变量未设置"}

    # 参数类型转换和验证
    try:
        # 处理字符串 "None" 的情况
        def normalize_none_param(param):
            if param is None or param == "" or param == "None" or param == "null":
                return None
            return param

        # 标准化参数
        include = normalize_none_param(include)
        exclude = normalize_none_param(exclude)
        freshness = normalize_none_param(freshness) or "noLimit"

        # 转换 summary 参数
        if summary is None or summary == "" or summary == "None":
            summary_bool = False
        else:
            summary_bool = summary.lower() in ("true", "1", "yes", "on")

        # 转换 count 参数
        if count is None or count == "" or count == "None":
            count_int = 10
        else:
            count_int = int(count)
            # 限制范围
            if count_int < 1:
                count_int = 1
            elif count_int > 50:
                count_int = 50

    except ValueError as e:
        logger.error(f"Parameter conversion error: {e}")
        return {
            "error": "parameter_error",
            "message": f"参数转换失败: {str(e)}",
            "details": "count参数必须是有效的数字"
        }

    # 记录处理后的参数
    logger.info(f"Processed parameters: freshness='{freshness}', summary_bool={summary_bool}, include='{include}', exclude='{exclude}', count_int={count_int}")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {BOCHAAI_API_KEY}"
    }
    
    payload = {
        "query": query,
        "freshness": freshness,
        "summary": summary_bool,
        "count": count_int
    }
    
    # 添加可选参数
    if include:
        payload["include"] = include
    if exclude:
        payload["exclude"] = exclude
    
    try:
        response = requests.post(
            BOCHAAI_API_URL,
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"API请求失败: {response.status_code}, {response.text}")
            return {
                "error": "api_request_failed",
                "message": f"API请求失败，状态码: {response.status_code}",
                "details": response.text
            }
    
    except requests.exceptions.Timeout:
        logger.error("API请求超时")
        return {"error": "timeout", "message": "API请求超时"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"网络请求异常: {str(e)}")
        traceback.print_exc()
        return {"error": "network_error", "message": f"网络请求异常: {str(e)}"}
    
    except Exception as e:
        logger.error(f"搜索过程中出现未知错误: {str(e)}")
        traceback.print_exc()
        return {"error": "unknown_error", "message": f"搜索过程中出现未知错误: {str(e)}"}


@mcp.tool()
async def baidu_websearch(query: str) -> dict:
    """
    使用百度web search接口进行网络搜索（测试）
    Returns:
        dict: 搜索结果
    """
    try:
        # 简单的API连通性测试
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {BAIDU_API_SIGN}"
        }
        
        test_payload = {
            "messages": [
                {
                    "content": query,
                    "role": "user"
                }
            ],
            "search_filter": {
                "match": {
                    "site": [
                        "www.weather.com.cn"
                    ]
                }
            }
        }
        
        response = requests.post(
            BAIDU_API_URL,
            headers=headers,
            json=test_payload,
            timeout=30
        )
        
        return response.json()
    
    except Exception as e:
        return {
            "status": "unhealthy",
            "api_accessible": False,
            "message": f"健康检查失败: {str(e)}"
        }

async def startup_event():
    """服务器启动事件"""
    logger.info("WebSearch MCP Server starting up...")
    logger.info(f"BOCHAAI_API_KEY configured: {'Yes' if BOCHAAI_API_KEY else 'No'}")
    logger.info(f"BAIDU_API_SIGN configured: {'Yes' if BAIDU_API_SIGN else 'No'}")

    # 确保 MCP 服务器状态初始化
    await server_state.ensure_initialized()
    logger.info("MCP Server initialization complete")

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    app = FastAPI(
        title="WebSearch MCP Server",
        description="Web search service using BOCHAAI and Baidu APIs",
        version="1.0.0"
    )

    # 添加启动事件
    app.add_event_handler("startup", startup_event)

    # 添加健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        return {
            "status": "healthy",
            "message": "WebSearch MCP Server is running",
            "version": "1.0.0",
            "tools": ["web_search", "baidu_websearch"]
        }

    @app.get("/mcp/status")
    async def mcp_status():
        """MCP 连接状态检查"""
        return {
            "mcp_initialized": server_state.initialized,
            "tools_available": ["web_search"],
            "server_info": {
                "name": "websearch",
                "version": "1.0.0"
            }
        }

    # HTTP API 端点
    @app.post("/api/web-search", response_model=WebSearchResponse)
    async def http_web_search(request: WebSearchRequest):
        """
        HTTP 方式调用网络搜索

        Args:
            request: 搜索请求参数

        Returns:
            WebSearchResponse: 搜索结果
        """
        try:
            # 调用原有的 MCP 工具函数
            result = await web_search(
                query=request.query,
                freshness=request.freshness,
                summary=request.summary,
                include=request.include,
                exclude=request.exclude,
                count=request.count
            )

            # 检查是否有错误
            if "error" in result:
                return WebSearchResponse(
                    success=False,
                    error=result.get("error"),
                    message=result.get("message", "搜索失败")
                )

            return WebSearchResponse(
                success=True,
                data=result
            )

        except Exception as e:
            logger.error(f"HTTP web search error: {str(e)}")
            traceback.print_exc()
            return WebSearchResponse(
                success=False,
                error="internal_error",
                message=f"内部错误: {str(e)}"
            )

    @app.post("/api/baidu-search", response_model=WebSearchResponse)
    async def http_baidu_search(request: BaiduSearchRequest):
        """
        HTTP 方式调用百度搜索

        Args:
            request: 搜索请求参数

        Returns:
            WebSearchResponse: 搜索结果
        """
        try:
            # 调用原有的 MCP 工具函数
            result = await baidu_websearch(request.query)

            # 检查是否有错误
            if "error" in result:
                return WebSearchResponse(
                    success=False,
                    error=result.get("error"),
                    message=result.get("message", "搜索失败")
                )

            return WebSearchResponse(
                success=True,
                data=result
            )

        except Exception as e:
            logger.error(f"HTTP baidu search error: {str(e)}")
            traceback.print_exc()
            return WebSearchResponse(
                success=False,
                error="internal_error",
                message=f"内部错误: {str(e)}"
            )

    @app.post("/api/web-search/stream")
    async def http_web_search_stream(request: WebSearchRequest):
        """
        HTTP 流式方式调用网络搜索

        Args:
            request: 搜索请求参数

        Returns:
            StreamingResponse: 流式搜索结果
        """
        async def generate_search_stream():
            try:
                # 发送开始信号
                yield f"data: {json.dumps({'type': 'start', 'message': '开始搜索...'})}\n\n"

                # 调用搜索函数
                result = await web_search(
                    query=request.query,
                    freshness=request.freshness,
                    summary=request.summary,
                    include=request.include,
                    exclude=request.exclude,
                    count=request.count
                )

                # 发送进度信号
                yield f"data: {json.dumps({'type': 'progress', 'message': '处理搜索结果...'})}\n\n"

                # 发送结果
                if "error" in result:
                    yield f"data: {json.dumps({'type': 'error', 'error': result.get('error'), 'message': result.get('message')})}\n\n"
                else:
                    yield f"data: {json.dumps({'type': 'result', 'data': result})}\n\n"

                # 发送完成信号
                yield f"data: {json.dumps({'type': 'done', 'message': '搜索完成'})}\n\n"

            except Exception as e:
                logger.error(f"Stream web search error: {str(e)}")
                yield f"data: {json.dumps({'type': 'error', 'error': 'internal_error', 'message': f'内部错误: {str(e)}'})}\n\n"

        return StreamingResponse(
            generate_search_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )

    @app.post("/api/baidu-search/stream")
    async def http_baidu_search_stream(request: BaiduSearchRequest):
        """
        HTTP 流式方式调用百度搜索

        Args:
            request: 搜索请求参数

        Returns:
            StreamingResponse: 流式搜索结果
        """
        async def generate_baidu_stream():
            try:
                # 发送开始信号
                yield f"data: {json.dumps({'type': 'start', 'message': '开始百度搜索...'})}\n\n"

                # 调用搜索函数
                result = await baidu_websearch(request.query)

                # 发送进度信号
                yield f"data: {json.dumps({'type': 'progress', 'message': '处理搜索结果...'})}\n\n"

                # 发送结果
                if "error" in result:
                    yield f"data: {json.dumps({'type': 'error', 'error': result.get('error'), 'message': result.get('message')})}\n\n"
                else:
                    yield f"data: {json.dumps({'type': 'result', 'data': result})}\n\n"

                # 发送完成信号
                yield f"data: {json.dumps({'type': 'done', 'message': '搜索完成'})}\n\n"

            except Exception as e:
                logger.error(f"Stream baidu search error: {str(e)}")
                yield f"data: {json.dumps({'type': 'error', 'error': 'internal_error', 'message': f'内部错误: {str(e)}'})}\n\n"

        return StreamingResponse(
            generate_baidu_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )

    @app.get("/api/docs")
    async def api_documentation():
        """
        API 文档端点

        Returns:
            dict: API 使用说明
        """
        return {
            "service": "WebSearch MCP Server",
            "version": "1.0.0",
            "description": "提供网络搜索服务，支持 MCP SSE 和 HTTP 两种调用方式",
            "endpoints": {
                "mcp_sse": {
                    "url": "/sse",
                    "method": "SSE",
                    "description": "MCP Server-Sent Events 接口",
                    "tools": ["web_search", "baidu_websearch"]
                },
                "http_apis": {
                    "web_search": {
                        "url": "/api/web-search",
                        "method": "POST",
                        "description": "HTTP 方式网络搜索",
                        "parameters": {
                            "query": "搜索查询词 (必需)",
                            "freshness": "时间范围 (可选, 默认: noLimit)",
                            "summary": "是否显示摘要 (可选, 默认: false)",
                            "include": "包含域名 (可选)",
                            "exclude": "排除域名 (可选)",
                            "count": "结果数量 (可选, 默认: 10)"
                        }
                    },
                    "web_search_stream": {
                        "url": "/api/web-search/stream",
                        "method": "POST",
                        "description": "HTTP 流式网络搜索",
                        "parameters": "同 web_search",
                        "response": "Server-Sent Events 流"
                    },
                    "baidu_search": {
                        "url": "/api/baidu-search",
                        "method": "POST",
                        "description": "HTTP 方式百度搜索",
                        "parameters": {
                            "query": "搜索查询词 (必需)"
                        }
                    },
                    "baidu_search_stream": {
                        "url": "/api/baidu-search/stream",
                        "method": "POST",
                        "description": "HTTP 流式百度搜索",
                        "parameters": "同 baidu_search",
                        "response": "Server-Sent Events 流"
                    }
                }
            },
            "examples": {
                "http_search": {
                    "url": "POST /api/web-search",
                    "body": {
                        "query": "Python编程",
                        "freshness": "oneWeek",
                        "summary": "true",
                        "count": "5"
                    }
                },
                "stream_search": {
                    "url": "POST /api/web-search/stream",
                    "body": {
                        "query": "人工智能最新发展",
                        "count": "10"
                    },
                    "response": "text/event-stream"
                }
            }
        }

    # 添加中间件确保 MCP 初始化
    @app.middleware("http")
    async def ensure_mcp_initialized(request, call_next):
        # 对于 MCP 相关的请求，确保初始化完成
        if request.url.path.startswith("/") and request.method == "POST":
            await server_state.ensure_initialized()
        response = await call_next(request)
        return response

    # 挂载 MCP SSE 应用到根路径
    app.mount("/", mcp.sse_app())

    # 启动服务器
    port = int(os.getenv("SERVER_PORT", "8003"))  # WebSearch 服务默认端口 8003
    logger.info(f"Starting WebSearch MCP Server on port {port}")
    logger.info("Available endpoints:")
    logger.info("  - MCP SSE: /sse")
    logger.info("  - HTTP API: /api/web-search")
    logger.info("  - Stream API: /api/web-search/stream")
    logger.info("  - API Docs: /api/docs")
    logger.info("  - Health Check: /health")

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        log_level="info",
        access_log=True
    )
