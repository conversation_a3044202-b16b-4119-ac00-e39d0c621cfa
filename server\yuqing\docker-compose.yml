
services:
  yuqing-mcp:
    build: .
    container_name: yuqing-mcp-server
    ports:
      - "8007:8007"
    environment:
      - ES_HOST=${ES_HOST:-************}
      - ES_PORT=${ES_PORT:-9200}
      - ES_USERNAME=${ES_USERNAME:-admin}
      - ES_PASSWORD=${ES_PASSWORD:-Wise<PERSON><PERSON>@666}
      - ES_INDEX=${ES_INDEX:-ybj_zy_data_articles_alias}
    restart: unless-stopped
    networks:
      - yuqing-network

networks:
  yuqing-network:
    driver: bridge
