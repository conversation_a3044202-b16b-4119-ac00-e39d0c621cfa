#!/usr/bin/env python3
"""
舆情MCP服务测试客户端
用于测试各种舆情查询功能
"""

import asyncio
import json
import requests
from typing import Dict, Any

class YuqingClient:
    def __init__(self, base_url: str = "http://localhost:8007"):
        self.base_url = base_url
    
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            print(f"调用工具: {tool_name}")
            print(f"参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
            
            # 模拟调用结果
            return {"status": "success", "tool": tool_name, "params": params}
        except Exception as e:
            return {"error": str(e)}

async def test_health_check(client: YuqingClient):
    """测试健康检查"""
    print("\n=== 测试健康检查 ===")
    result = await client.call_tool("health_check", {})
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

async def test_get_sentiment_data(client: YuqingClient):
    """测试获取单个实体舆情数据"""
    print("\n=== 测试获取单个实体舆情数据 ===")
    params = {
        "entity_id": "4915",
        "limit": 5
    }
    result = await client.call_tool("get_sentiment_data", params)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

async def test_get_sentiment_list(client: YuqingClient):
    """测试批量获取多实体舆情数据"""
    print("\n=== 测试批量获取多实体舆情数据 ===")
    params = {
        "entity_ids": ["4915", "4907"],
        "limit_per_entity": 3
    }
    result = await client.call_tool("get_sentiment_list", params)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

async def test_get_sentiment_by_time_range(client: YuqingClient):
    """测试根据时间范围获取舆情数据"""
    print("\n=== 测试根据时间范围获取舆情数据 ===")
    params = {
        "entity_id": "4915",
        "start_time": "2025-07-24 00:00:00",
        "end_time": "2025-07-31 23:59:59",
        "limit": 10
    }
    result = await client.call_tool("get_sentiment_by_time_range", params)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

async def test_get_sentiment_aggregation(client: YuqingClient):
    """测试获取舆情聚合统计"""
    print("\n=== 测试获取舆情聚合统计 ===")
    params = {
        "entity_id": "4915",
        "agg_interval": "day",
        "start_time": "2025-07-24 00:00:00",
        "end_time": "2025-07-31 23:59:59"
    }
    result = await client.call_tool("get_sentiment_aggregation", params)
    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")

async def main():
    """主测试函数"""
    print("舆情MCP服务测试开始...")
    print("=" * 50)
    
    client = YuqingClient()
    
    # 执行各种测试
    await test_health_check(client)
    await test_get_sentiment_data(client)
    await test_get_sentiment_list(client)
    await test_get_sentiment_by_time_range(client)
    await test_get_sentiment_aggregation(client)
    
    print("=" * 50)
    print("所有测试完成！")
    print("\n注意：这是模拟测试，实际使用时需要：")
    print("1. 确保Elasticsearch服务正在运行")
    print("2. 配置正确的连接参数")
    print("3. 确保舆情索引存在并有数据")
    print("4. 使用真实的MCP客户端连接")

if __name__ == "__main__":
    asyncio.run(main())
