# MCP工具调用Utils使用说明

这个工具包提供了一个简单易用的接口来调用MCP (Model Context Protocol) 工具，使用MultiServerMCPClient作为底层实现。

## 功能特性

- 🚀 **简单易用**: 只需提供IP、端口和工具名即可调用
- 🔧 **多服务器支持**: 可以同时管理多个MCP服务器
- 🛡️ **错误处理**: 完善的错误处理和日志记录
- 📋 **工具发现**: 自动列出所有可用工具
- 🔄 **异步支持**: 完全异步实现，性能优异

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 基本使用

#### 方式一：便捷函数（推荐用于单次调用）

```python
import asyncio
from mcp_utils import call_mcp_tool

async def main():
    # 调用web搜索工具
    result = await call_mcp_tool(
        ip="localhost",
        port=8003,
        tool_name="web_search",
        query="Python编程",
        count=3
    )
    print(result)

asyncio.run(main())
```

#### 方式二：MCPToolCaller类（推荐用于多次调用）

```python
import asyncio
from mcp_utils import MCPToolCaller

async def main():
    caller = MCPToolCaller()
    
    # 添加服务器配置
    caller.add_server("websearch", "localhost", 8003)
    caller.add_server("weather", "localhost", 8001)
    
    async with caller as client:
        # 列出所有工具
        tools = await client.list_tools()
        print(f"可用工具: {[tool['name'] for tool in tools]}")
        
        # 调用工具
        result = await client.call_tool("web_search", query="AI发展")
        print(result)

asyncio.run(main())
```

## API参考

### call_mcp_tool()

便捷函数，用于单次工具调用。

```python
async def call_mcp_tool(
    ip: str,                    # 服务器IP地址
    port: int,                  # 服务器端口
    tool_name: str,             # 工具名称
    server_name: str = "default", # 服务器名称（可选）
    transport: str = "sse",     # 传输方式（sse或stdio）
    **kwargs                    # 工具参数
) -> Dict[str, Any]
```

### list_mcp_tools()

列出MCP服务器的所有可用工具。

```python
async def list_mcp_tools(
    ip: str,                    # 服务器IP地址
    port: int,                  # 服务器端口
    server_name: str = "default", # 服务器名称（可选）
    transport: str = "sse"      # 传输方式（sse或stdio）
) -> List[Dict[str, Any]]
```

### MCPToolCaller类

用于管理多个MCP服务器连接的主要类。

#### 方法

- `add_server(server_name, ip, port, transport="sse")`: 添加服务器配置
- `call_tool(tool_name, **kwargs)`: 调用指定工具
- `list_tools()`: 列出所有可用工具

## 使用示例

### 1. Web搜索示例

```python
# 调用web搜索工具
result = await call_mcp_tool(
    ip="localhost",
    port=8003,
    tool_name="web_search",
    query="人工智能最新发展",
    freshness="noLimit",
    count=5,
    summary=False
)
```

### 2. 天气查询示例

```python
# 调用天气查询工具
result = await call_mcp_tool(
    ip="localhost",
    port=8001,
    tool_name="get_weather",
    location="北京"
)
```

### 3. 实体信息查询示例

```python
# 调用实体信息查询工具
result = await call_mcp_tool(
    ip="localhost",
    port=8002,
    tool_name="get_entity_info",
    company_alias="腾讯"
)
```

### 4. 多服务器管理示例

```python
caller = MCPToolCaller()

# 添加多个服务器
caller.add_server("websearch", "localhost", 8003)
caller.add_server("weather", "localhost", 8001)
caller.add_server("entity", "localhost", 8002)

async with caller as client:
    # 调用不同服务器的工具
    web_result = await client.call_tool("web_search", query="AI")
    weather_result = await client.call_tool("get_weather", location="上海")
    entity_result = await client.call_tool("get_entity_info", company_alias="阿里")
```

## 错误处理

工具调用返回的结果格式：

### 成功响应
```python
{
    "success": True,
    "result": "工具执行结果",
    "tool_name": "工具名称",
    "parameters": {"参数": "值"}
}
```

### 错误响应
```python
{
    "error": "错误类型",
    "message": "错误描述",
    "tool_name": "工具名称",
    "parameters": {"参数": "值"}
}
```

## 测试

运行测试文件：

```bash
python test_mcp_utils.py
```

## 注意事项

1. 确保目标MCP服务器正在运行
2. 检查IP地址和端口配置是否正确
3. 确认工具名称和参数格式
4. 建议在生产环境中添加重试机制和更详细的错误处理

## 支持的传输方式

- **SSE (Server-Sent Events)**: 默认方式，适用于HTTP服务器
- **STDIO**: 适用于命令行程序

## 常见问题

**Q: 如何查看可用的工具？**
A: 使用 `list_mcp_tools()` 函数或 `client.list_tools()` 方法。

**Q: 工具调用失败怎么办？**
A: 检查返回结果中的 `error` 字段，根据错误信息进行调试。

**Q: 如何添加新的服务器？**
A: 使用 `caller.add_server()` 方法添加新的服务器配置。
