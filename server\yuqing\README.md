# 舆情 MCP 服务

这是一个基于MCP (Model Context Protocol) 的舆情数据查询服务，提供从Elasticsearch中查询舆情数据的功能。

## 功能特性

- **单实体舆情查询**: 获取指定实体的舆情数据
- **多实体批量查询**: 批量获取多个实体的舆情数据
- **时间范围查询**: 基于时间范围进行舆情数据查询
- **聚合统计**: 提供按时间间隔的舆情数据聚合统计
- **健康检查**: 监控Elasticsearch连接状态和索引状态

## 快速开始

### 1. 环境配置

复制环境变量示例文件并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置你的Elasticsearch连接信息：

```env
ES_HOST=**************
ES_PORT=9600
ES_USERNAME=your_username
ES_PASSWORD=your_password
ES_INDEX=yuqing_index
MCP_PORT=8007
LOG_LEVEL=INFO
```

### 2. 使用Docker部署（推荐）

#### 方式一：使用docker-compose

```bash
# 构建并启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f yuqing-mcp
```

#### 方式二：使用Docker直接运行

```bash
# 构建镜像
docker build -t yuqing-mcp .

# 运行容器
docker run -d \
  --name yuqing-mcp-server \
  -p 8007:8007 \
  --env-file .env \
  yuqing-mcp
```

### 3. 本地开发运行

```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python yuqing_mcp_server.py
```

## API工具说明

### 1. get_sentiment_data
获取单个实体舆情数据

**参数:**
- `entity_id` (string): 实体ID
- `limit` (integer, 可选): 返回条数限制，默认10

**返回:**
```json
{
  "sentiment_data": [
    {
      "id": "舆情数据ID",
      "entity_id": "实体ID",
      "content": "舆情内容",
      "source": "来源",
      "sentiment_label": "情感标签",
      "publish_time": "发布时间",
      "url": "原文链接",
      "title": "标题"
    }
  ],
  "total": 100
}
```

### 2. get_sentiment_list
批量获取多实体舆情数据

**参数:**
- `entity_ids` (array[string]): 实体ID列表
- `limit_per_entity` (integer, 可选): 每个实体返回条数，默认5

### 3. get_sentiment_by_time_range
根据时间范围获取实体舆情数据

**参数:**
- `entity_id` (string): 实体ID
- `start_time` (string, 可选): 开始时间，格式：YYYY-MM-DD HH:MM:SS
- `end_time` (string, 可选): 结束时间，格式：YYYY-MM-DD HH:MM:SS
- `limit` (integer, 可选): 返回条数限制，默认10

### 4. get_sentiment_aggregation
获取实体舆情数据聚合统计

**参数:**
- `entity_id` (string): 实体ID
- `agg_interval` (string, 可选): 聚合间隔，支持：day、week、month、year，默认day
- `start_time` (string, 可选): 开始时间
- `end_time` (string, 可选): 结束时间

**返回:**
```json
{
  "aggregation_data": [
    {
      "date": "2025-07-31",
      "doc_count": 15
    }
  ],
  "total": 100,
  "agg_interval": "day"
}
```

### 5. health_check
健康检查

**返回:**
```json
{
  "status": "healthy",
  "elasticsearch": "connected",
  "index_exists": true,
  "index_name": "yuqing_index"
}
```

## 测试

运行测试客户端：

```bash
python test_client.py
```

## 数据结构说明

### Elasticsearch索引结构
服务期望的Elasticsearch索引结构包含以下字段：
- `Rowkey`: 舆情数据ID
- `New_Module_Ids`: 实体ID（嵌套结构）
- `Content`: 舆情内容
- `Source`: 来源
- `Ner_Flag`: 情感标签
- `Pub_Time`: 发布时间
- `Url`: 原文链接
- `Title`: 标题

### 嵌套查询配置
- `nested_path`: "New_Module_Ids"
- `nested_path_type`: "company"
- `date_field`: "Pub_Time"

## 故障排除

1. **连接失败**: 检查Elasticsearch服务是否运行，网络是否可达
2. **索引不存在**: 确认配置的索引名称正确，索引已创建
3. **查询无结果**: 检查实体ID是否正确，数据是否存在
4. **时间格式错误**: 确保时间格式为 YYYY-MM-DD HH:MM:SS

## 配置说明

### 环境变量
- `ES_HOST`: Elasticsearch主机地址
- `ES_PORT`: Elasticsearch端口
- `ES_USERNAME`: Elasticsearch用户名（可选）
- `ES_PASSWORD`: Elasticsearch密码（可选）
- `ES_INDEX`: 舆情数据索引名称
- `MCP_PORT`: MCP服务端口
- `LOG_LEVEL`: 日志级别

### 配置文件
`config.json` 文件包含Elasticsearch连接的详细配置，与环境变量配置二选一使用。
