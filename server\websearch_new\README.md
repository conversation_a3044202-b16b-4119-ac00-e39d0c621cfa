# WebSearch MCP Server

这是一个基于 MCP (Model Context Protocol) 的网络搜索服务，提供多种调用方式以提高兼容性。

## 功能特性

- **MCP SSE 接口**: 支持 Server-Sent Events 方式调用
- **HTTP REST API**: 支持标准 HTTP POST 请求
- **流式 HTTP API**: 支持 Server-Sent Events 流式响应
- **多搜索引擎**: 支持博查AI和百度搜索
- **参数灵活**: 支持时间范围、域名过滤、结果数量等参数

## 快速开始

### 1. 环境配置

创建 `.env` 文件并配置 API 密钥：

```env
BOCHAAI_API_KEY=your_bochaai_api_key
BAIDU_API_SIGN=your_baidu_api_sign
SERVER_PORT=8003
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动服务

```bash
python websearch_mcp_server.py
```

服务将在 `http://localhost:8003` 启动。

## API 接口说明

### MCP SSE 接口

**端点**: `/sse`  
**协议**: Server-Sent Events  
**用途**: 与 MCP 客户端集成

### HTTP REST API

#### 1. 网络搜索

**端点**: `POST /api/web-search`

**请求体**:
```json
{
    "query": "搜索关键词",
    "freshness": "oneWeek",
    "summary": "true",
    "include": "example.com",
    "exclude": "spam.com",
    "count": "10"
}
```

**响应**:
```json
{
    "success": true,
    "data": {
        "results": [...],
        "query": "搜索关键词"
    }
}
```

#### 2. 百度搜索

**端点**: `POST /api/baidu-search`

**请求体**:
```json
{
    "query": "搜索关键词"
}
```

### 流式 HTTP API

#### 1. 流式网络搜索

**端点**: `POST /api/web-search/stream`  
**响应类型**: `text/event-stream`

**请求体**: 同 `/api/web-search`

**流式响应示例**:
```
data: {"type": "start", "message": "开始搜索..."}

data: {"type": "progress", "message": "处理搜索结果..."}

data: {"type": "result", "data": {...}}

data: {"type": "done", "message": "搜索完成"}
```

#### 2. 流式百度搜索

**端点**: `POST /api/baidu-search/stream`  
**响应类型**: `text/event-stream`

### 其他端点

- **健康检查**: `GET /health`
- **MCP 状态**: `GET /mcp/status`
- **API 文档**: `GET /api/docs`

## 参数说明

### 网络搜索参数

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| query | string | 是 | - | 搜索查询词 |
| freshness | string | 否 | "noLimit" | 时间范围 (noLimit/oneDay/oneWeek/oneMonth/oneYear/YYYY-MM-DD..YYYY-MM-DD) |
| summary | string | 否 | "false" | 是否显示摘要 ("true"/"false") |
| include | string | 否 | null | 包含的域名，多个用\|或,分隔 |
| exclude | string | 否 | null | 排除的域名，多个用\|或,分隔 |
| count | string | 否 | "10" | 返回结果数量 (1-50) |

## 使用示例

### Python 客户端示例

```python
import aiohttp
import asyncio

async def search_example():
    async with aiohttp.ClientSession() as session:
        # HTTP 搜索
        payload = {
            "query": "Python编程教程",
            "freshness": "oneWeek",
            "count": "5"
        }
        
        async with session.post("http://localhost:8003/api/web-search", json=payload) as response:
            result = await response.json()
            print(result)

# 运行示例
asyncio.run(search_example())
```

### 流式搜索示例

```python
import aiohttp
import json

async def stream_search_example():
    async with aiohttp.ClientSession() as session:
        payload = {"query": "人工智能", "count": "3"}
        
        async with session.post("http://localhost:8003/api/web-search/stream", json=payload) as response:
            async for line in response.content:
                line = line.decode('utf-8').strip()
                if line.startswith('data: '):
                    data = json.loads(line[6:])
                    print(f"类型: {data['type']}, 消息: {data.get('message', '')}")
```

### curl 示例

```bash
# HTTP 搜索
curl -X POST http://localhost:8003/api/web-search \
  -H "Content-Type: application/json" \
  -d '{"query": "Python编程", "count": "3"}'

# 流式搜索
curl -X POST http://localhost:8003/api/web-search/stream \
  -H "Content-Type: application/json" \
  -d '{"query": "人工智能", "count": "2"}'
```

## 测试

运行测试脚本验证所有功能：

```bash
python test_http_api.py
```

## Docker 部署

```bash
# 构建镜像
docker build -t websearch-mcp:latest .

# 运行容器
docker run -d \
  --name websearch-mcp-server \
  -p 8003:8003 \
  --env-file .env \
  websearch-mcp:latest
```

## 兼容性

- **MCP 客户端**: 通过 SSE 接口完全兼容
- **HTTP 客户端**: 支持任何能发送 HTTP 请求的客户端
- **流式客户端**: 支持 Server-Sent Events 的客户端
- **编程语言**: Python, JavaScript, Java, Go, Rust 等

## 错误处理

所有 API 都包含完善的错误处理机制：

- 参数验证错误
- 网络请求超时
- API 密钥错误
- 服务不可用

错误响应格式：
```json
{
    "success": false,
    "error": "error_code",
    "message": "详细错误信息"
}
```

## 性能优化

- 异步处理所有请求
- 连接池复用
- 请求超时控制
- 内存使用优化

## 日志记录

服务包含详细的日志记录，便于调试和监控：

- 请求参数记录
- 响应时间统计
- 错误详情记录
- 性能指标监控
