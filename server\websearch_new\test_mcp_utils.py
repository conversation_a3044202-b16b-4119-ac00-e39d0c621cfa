#!/usr/bin/env python3
"""
测试MCP工具调用utils
"""

import asyncio
import sys
import os
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from mcp_utils import MCPToolCaller, call_mcp_tool, list_mcp_tools

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_single_tool_call():
    """测试单个工具调用"""
    print("=== 测试单个工具调用 ===")

    try:
        # 测试调用websearch工具
        result = await call_mcp_tool(
            ip="localhost",
            port=8007,  # websearch_new服务端口
            tool_name="get_sentiment_data",
            entity_id="4743"
        )
        #     result = await call_mcp_tool(
        #     ip="*************",
        #     port=8006,  # websearch_new服务端口
        #     tool_name="get_entity_id",
        #     company_name="阳光保险集团股份有限公司"
        # )
        

        # JSON格式化输出结果
        import json
        print("🔍 公司搜索结果:")
        print("=" * 50)

        if result.get("success"):
            print("✅ 调用成功!")
            print(f"🏢 工具名称: {result.get('tool_name')}")
            print(f"📝 查询参数: {json.dumps(result.get('parameters', {}), ensure_ascii=False, indent=2)}")

            # 解析并格式化工具返回的结果
            tool_result = result.get('result', '')
            if isinstance(tool_result, str):
                try:
                    # 尝试解析JSON字符串
                    parsed_result = json.loads(tool_result)
                    print("📊 搜索结果:")
                    print(json.dumps(parsed_result, ensure_ascii=False, indent=2))
                except json.JSONDecodeError:
                    print(f"📄 原始结果: {tool_result}")
            else:
                print("📊 搜索结果:")
                print(json.dumps(tool_result, ensure_ascii=False, indent=2))
        else:
            print("❌ 调用失败!")
            print(f"🚫 错误类型: {result.get('error', '未知错误')}")
            print(f"💬 错误信息: {result.get('message', '无详细信息')}")
            if result.get('available_tools'):
                print(f"🔧 可用工具: {result.get('available_tools')}")

        print("=" * 50)

    except Exception as e:
        print(f"❌ 单个工具调用测试失败: {e}")
        import traceback
        print("🔍 详细错误信息:")
        traceback.print_exc()

async def test_list_tools():
    """测试列出工具"""
    print("\n=== 测试列出工具 ===")
    
    try:
        # 列出websearch服务的所有工具
        tools = await list_mcp_tools(
            ip="localhost",
            port=8003
        )
        print(f"可用工具列表:")
        for tool in tools:
            print(f"  - {tool['name']}: {tool.get('description', '无描述')}")
            
    except Exception as e:
        print(f"列出工具测试失败: {e}")

async def test_multiple_servers():
    """测试多服务器管理"""
    print("\n=== 测试多服务器管理 ===")
    
    try:
        caller = MCPToolCaller()
        
        # 添加多个服务器配置
        caller.add_server("websearch", "localhost", 8003)
        caller.add_server("weather", "localhost", 8001)
        caller.add_server("entity", "localhost", 8002)
        
        async with caller as client:
            # 列出所有可用工具
            tools = await client.list_tools()
            print(f"所有服务器的工具数量: {len(tools)}")
            
            # 调用不同服务器的工具
            print("\n调用websearch工具:")
            web_result = await client.call_tool(
                "web_search",
                query="人工智能最新发展",
                count=2
            )
            print(f"Web搜索: {web_result.get('success', False)}")
            
            print("\n调用weather工具:")
            weather_result = await client.call_tool(
                "get_weather",
                location="北京"
            )
            print(f"天气查询: {weather_result.get('success', False)}")
            
            print("\n调用entity工具:")
            entity_result = await client.call_tool(
                "get_entity_info",
                company_alias="阿里巴巴"
            )
            print(f"实体查询: {entity_result.get('success', False)}")
            
    except Exception as e:
        print(f"多服务器测试失败: {e}")

async def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        # 测试调用不存在的工具
        result = await call_mcp_tool(
            ip="localhost",
            port=8003,
            tool_name="non_existent_tool",
            param1="test"
        )
        print(f"调用不存在工具的结果: {result}")
        
        # 测试连接不存在的服务器
        result = await call_mcp_tool(
            ip="localhost",
            port=9999,  # 不存在的端口
            tool_name="any_tool"
        )
        print(f"连接不存在服务器的结果: {result}")
        
    except Exception as e:
        print(f"错误处理测试: {e}")

async def main():
    """主测试函数"""
    print("开始测试MCP工具调用utils...")
    print("=" * 50)
    
    # 运行各种测试
    await test_single_tool_call()
    # await test_list_tools()
    # await test_multiple_servers()
    # await test_error_handling()
    
    # print("=" * 50)
    # print("测试完成！")
    
    # print("\n使用说明:")
    # print("1. 确保相关MCP服务器正在运行")
    # print("2. 检查服务器的IP和端口配置")
    # print("3. 根据实际情况调整工具名称和参数")

if __name__ == "__main__":
    asyncio.run(main())
