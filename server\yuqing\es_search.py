# encoding=utf-8

import json
from datetime import datetime
from dateutil.relativedelta import relativedelta
from elasticsearch import Elasticsearch


# 读取json配置文件
def read_json_config(file_path):
    with open(file_path, "r") as file:
        config = json.load(file)
    return config


# 获取过去的时间，无参数获取当前时间
def get_ago_date(years=None, months=None, weeks=None, days=None):
    # 获取当前日期时间
    current_date = datetime.now()
    # years值代表几年前
    if years:
        ago_time = relativedelta(years=years)
    # months值代表几个月前
    elif months:
        ago_time = relativedelta(months=months)
    # weeks值代表几周前
    elif weeks:
        ago_time = relativedelta(weeks=weeks)
    # days值代表几天前
    elif days:
        ago_time = relativedelta(days=days)
    else:
        return current_date.strftime("%Y-%m-%d %H:%M:%S")
    # 过去日期格式处理
    ago_date = (current_date - ago_time).strftime("%Y-%m-%d %H:%M:%S")
    return ago_date


# 构建查询体
def build_es_query(nested_dict, agg_dict=None, min_date=None, max_date=None, sort_desc=True, nested_agg=True):
    # 自定义的日期字段，用于时间限制和数据排序
    date_field = nested_dict["date_field"]
    # 主查询body体
    query_body = {
        # 展示查询数据量
        "track_total_hits": True,
        "query": {
            "bool": {
                # 用于过滤条件
                "must": [],
                # 用于过滤时间
                "filter": []
            }
        }
    }
    # nested嵌套查询
    nested_query = {
        "nested": {
            "path": nested_dict["nested_path"],
            "query": {
                "bool": {
                    # 用于单个或多实体id
                    "should": [],
                }
            }
        }
    }
    if "entity_id" in nested_dict or "entity_id_list" in nested_dict:
        match_field = "%s.%s" % (nested_dict["nested_path"], nested_dict["nested_path_type"])
        if "entity_id" in nested_dict:
            nested_query["nested"]["query"]["bool"]["should"].append({
                "match": {
                    match_field: nested_dict["entity_id"]
                }
            })
            # 将嵌套查询添加到主查询的must部分
            query_body["query"]["bool"]["must"].append(nested_query)
        elif "entity_id_list" in nested_dict:
            for nested_id in nested_dict["entity_id_list"]:
                nested_query["nested"]["query"]["bool"]["should"].append({
                    "match": {
                        match_field: nested_id
                    }
                })
            # 将嵌套查询添加到主查询的filter部分
            query_body["query"]["bool"]["must"].append(nested_query)
    # 聚合查询
    if agg_dict:
        # 日期聚合查询
        if agg_dict["agg_type"] == "date_histogram":
            query_body["aggs"] = {
                agg_dict["agg_name"]: {
                    agg_dict["agg_type"]: {
                        "field": agg_dict["agg_field"],
                        "calendar_interval": agg_dict["agg_interval"],
                        "format": agg_dict["format"]
                    }
                }
            }
    # 时间限制最小日期
    if min_date:
        query_body["query"]["bool"]["filter"].append({
            "range": {
                date_field: {
                    "gte": min_date
                }
            }
        })
    # 时间限制最大日期
    if max_date:
        query_body["query"]["bool"]["filter"].append({
            "range": {
                date_field: {
                    "lte": max_date
                }
            }
        })
    # 非聚合查询时默认根据日期字段倒序
    if not agg_dict and sort_desc:
        query_body["sort"] = [
            {
                date_field: {
                    "order": "desc"
                }
            }
        ]
    print(query_body)
    return query_body


# 解析response
def parse_response(response):
    print(response["hits"]["total"]["value"])
    if not response["hits"]:
        return None
    # 返回的语义数据
    sentiment_data = list()
    # 获取每个文档的详细信息
    for hit in response["hits"]["hits"]:
        document = dict()
        doc_source = hit["_source"]
        # 舆情数据ID
        document["id"] = doc_source["Rowkey"]
        # 实体ID
        document["entity_id"] = doc_source["New_Module_Ids"]
        # 舆情内容
        document["content"] = doc_source["Content"]
        # 来源
        document["source"] = doc_source["Source"]
        # sentiment_label
        document["sentiment_label"] = doc_source["Ner_Flag"]
        # 发布时间
        document["publish_time"] = doc_source["Pub_Time"]
        # 原文链接
        document["url"] = doc_source["Url"]
        # 标题
        document["title"] = doc_source["Title"]
        sentiment_data.append(document)
    return sentiment_data


# 解析聚合response
def parse_agg_response(response, agg_dict):
    # 日期聚合解析
    if agg_dict["agg_type"] == "date_histogram":
        buckets = response["aggregations"]["dates_aggregation"]["buckets"]
        if not buckets:
            return None
        sentiment_agg_data = list()
        for bucket in buckets:
            document = dict()
            # 聚合日期
            document["date"] = bucket['key_as_string']
            # 聚合数据量
            document["doc_count"] = bucket['doc_count']
            sentiment_agg_data.append(document)
        return sentiment_agg_data


if __name__ == '__main__':
    # 读取json配置文件
    config = read_json_config("config.json")
    # 创建Elasticsearch客户端
    es = Elasticsearch(hosts=config["hosts"], http_auth=(config["user"], config["password"]), port=config["port"])
    # 嵌套查询过滤条件和字段集合
    nested_dict = dict()
    # 语义实体id
    nested_dict["entity_id"] = 4915
    # nested_dict["entity_id_list"] = [4915, 4907]
    # 用于过滤和查询的日期字段，发布时间
    nested_dict["date_field"] = "Pub_Time"
    # 嵌套查询字段，语义id集合
    nested_dict["nested_path"] = "New_Module_Ids"
    # 嵌套查询字段的类型（实体id对应company字段）
    nested_dict["nested_path_type"] = "company"
    # 聚合查询过滤条件和字段集合
    agg_dict = dict()
    # 聚合查询名称
    agg_dict["agg_name"] = "dates_aggregation"
    # 聚合类型
    agg_dict["agg_type"] = "date_histogram"
    # 聚合字段
    agg_dict["agg_field"] = "Pub_Time"
    # 聚合日期间隔year、month、day等（用于date_histogram类型聚合）
    agg_dict["agg_interval"] = "day"
    # 聚合日期格式
    agg_dict["format"] = "yyyy-MM-dd"
    # # 构建查询体
    # query = build_es_query(nested_dict=nested_dict, min_date=get_ago_date(weeks=1), max_date=get_ago_date(days=1))
    # # 执行战鹰终端索引查询，请求超时时间1分钟
    # response = es.search(index=config["index"], body=query, size=5, request_timeout=60)
    # # 解析response
    # sentiment_data = parse_response(response)
    # 构建聚合查询体
    agg_query = build_es_query(nested_dict=nested_dict, agg_dict=agg_dict, min_date=get_ago_date(weeks=1), max_date=get_ago_date(days=1))
    # 执行战鹰终端索引聚合查询，请求超时时间1分钟
    agg_response = es.search(index=config["index"], body=agg_query, size=0, request_timeout=60)
    # 解析聚合response
    sentiment_agg_data = parse_agg_response(agg_response, agg_dict)
    print(sentiment_agg_data)